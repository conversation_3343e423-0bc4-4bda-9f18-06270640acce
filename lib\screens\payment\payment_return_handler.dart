import 'package:callitris/screens/boutique/my_orders_screen.dart';
import 'package:callitris/services/order_service.dart';
import 'package:flutter/material.dart';
import 'package:callitris/services/deep_link_service.dart';
import 'package:callitris/services/auth_service.dart';
import 'package:callitris/services/app_state_service.dart';
import 'package:callitris/services/wallet_service.dart';
import 'package:callitris/config/api_config.dart';

/// Widget pour gérer les retours de paiement via deep links
class PaymentReturnHandler extends StatefulWidget {
  final Widget child;

  const PaymentReturnHandler({Key? key, required this.child}) : super(key: key);

  @override
  State<PaymentReturnHandler> createState() => _PaymentReturnHandlerState();
}

class _PaymentReturnHandlerState extends State<PaymentReturnHandler>
    with WidgetsBindingObserver {
  static const String _tag = '[PaymentReturnHandler]';
  bool _wasInBackground = false;

  @override
  void initState() {
    super.initState();

    // Écouter les changements d'état de l'application
    WidgetsBinding.instance.addObserver(this);

    _initializeDeepLinkHandling();
  }

  /// Initialise la gestion des deep links
  void _initializeDeepLinkHandling() {
    // Initialiser le service de deep links avec un callback
    DeepLinkService.initialize(onLinkReceived: _handleDeepLink);

    // Écouter le stream des deep links
    DeepLinkService.linkStream.listen(
      _handleDeepLink,
      onError: (error) {
        print('$_tag: Erreur dans le stream de deep links: $error');
      },
    );
  }

  /// Gère un deep link reçu
  void _handleDeepLink(String link) {
    print('$_tag: Deep link reçu: $link');

    final uri = Uri.tryParse(link);
    if (uri == null) {
      print('$_tag: URL invalide: $link');
      return;
    }

    // Vérifier si c'est un deep link de paiement
    if (uri.scheme == ApiConfig.appScheme && uri.host == 'payment') {
      _handlePaymentDeepLink(uri);
    }
  }

  /// Gère spécifiquement les deep links de paiement
  void _handlePaymentDeepLink(Uri uri) {
    final String path = uri.path;
    final Map<String, String> params = uri.queryParameters;

    switch (path) {
      case '/success':
        _showPaymentSuccessDialog(params);
        break;
      case '/failure':
        _showPaymentFailureDialog(params);
        break;
      case '/cancel':
        _showPaymentCancelDialog(params);
        break;
      case '/return':
        _handlePaymentReturn(params);
        break;
      default:
        print('$_tag: Chemin de paiement non reconnu: $path');
    }
  }

  /// Gère le retour de paiement (vérification du statut)
  void _handlePaymentReturn(Map<String, String> params) async {
    try {
      print('$_tag: Vérification du statut de paiement...');

      // Afficher un indicateur de chargement
      _showLoadingDialog();

      // NOUVEAU: Rafraîchir toutes les données de l'application
      await _refreshAllAppData();

      // Attendre un peu pour que l'utilisateur voie le message
      await Future.delayed(const Duration(seconds: 1));

      // Vérifier le statut du paiement depuis le stockage local
      final String? paymentStatus = await OrderService.readData(
        'payment_status',
      );

      // Fermer le dialog de chargement
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Traiter selon le statut
      if (paymentStatus == 'success') {
        _showPaymentSuccessDialog(params);
      } else if (paymentStatus == 'failed') {
        _showPaymentFailureDialog(params);
      } else if (paymentStatus == 'cancelled') {
        _showPaymentCancelDialog(params);
      } else {
        // Statut indéterminé, demander à l'utilisateur de vérifier
        _showPaymentPendingDialog(params);
      }
    } catch (e) {
      print('$_tag: Erreur lors de la vérification du paiement: $e');
      if (mounted) {
        Navigator.of(context).pop(); // Fermer le loading
        _showPaymentErrorDialog();
      }
    }
  }

  /// Rafraîchit toutes les données de l'application après un retour de paiement
  Future<void> _refreshAllAppData() async {
    try {
      print('$_tag: Rafraîchissement de toutes les données après paiement...');

      // Rafraîchir les commandes (le plus important)
      await OrderService.refreshOrders(silent: true);

      // Rafraîchir les données utilisateur si disponible
      try {
        final userData = await AuthService.getUserData();
        if (userData != null && userData['success'] == true) {
          print('$_tag: Données utilisateur rafraîchies');
        }
      } catch (e) {
        print(
          '$_tag: Erreur lors du rafraîchissement des données utilisateur: $e',
        );
      }

      // Rafraîchir la monnaie/wallet si disponible
      try {
        await WalletService.getUserMonnaie();
        print('$_tag: Données de monnaie rafraîchies');
      } catch (e) {
        print('$_tag: Erreur lors du rafraîchissement de la monnaie: $e');
      }

      // Utiliser AppStateService pour rafraîchir globalement si disponible
      try {
        await AppStateService.instance.refreshAllData(silent: true);
        print('$_tag: État global de l\'application rafraîchi');
      } catch (e) {
        print('$_tag: Erreur lors du rafraîchissement global: $e');
      }

      print('$_tag: Rafraîchissement terminé avec succès');
    } catch (e) {
      print('$_tag: Erreur lors du rafraîchissement global des données: $e');
    }
  }

  /// Affiche un dialog de chargement
  void _showLoadingDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => PopScope(
            canPop: false,
            child: const AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Vérification du paiement...'),
                ],
              ),
            ),
          ),
    );
  }

  /// Affiche un dialog de succès de paiement
  void _showPaymentSuccessDialog(Map<String, String> params) {
    if (!mounted) return;

    final String? transactionId = params['transaction_id'] ?? params['id'];
    final String? amount = params['amount'] ?? params['montant'];

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => PopScope(
            canPop: false,
            child: AlertDialog(
              icon: const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 64,
              ),
              title: const Text('Paiement Réussi'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Votre paiement a été traité avec succès.'),
                  if (transactionId != null) ...[
                    const SizedBox(height: 8),
                    Text('Transaction: $transactionId'),
                  ],
                  if (amount != null) ...[
                    const SizedBox(height: 4),
                    Text('Montant: $amount FCFA'),
                  ],
                ],
              ),
              actions: [
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    _navigateToOrdersPage(context);
                  },
                  child: const Text('Voir mes commandes'),
                ),
              ],
            ),
          ),
    );
  }

  /// Affiche un dialog d'échec de paiement
  void _showPaymentFailureDialog(Map<String, String> params) {
    if (!mounted) return;

    final String? errorMessage = params['error_message'] ?? params['message'];

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => PopScope(
            canPop: false,
            child: AlertDialog(
              icon: const Icon(Icons.error, color: Colors.red, size: 64),
              title: const Text('Paiement Échoué'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Votre paiement n\'a pas pu être traité.'),
                  if (errorMessage != null) ...[
                    const SizedBox(height: 8),
                    Text('Erreur: $errorMessage'),
                  ],
                  const SizedBox(height: 8),
                  const Text('Veuillez réessayer ou contacter le support.'),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Fermer'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _retryPayment();
                  },
                  child: const Text('Réessayer'),
                ),
              ],
            ),
          ),
    );
  }

  /// Affiche un dialog d'annulation de paiement
  void _showPaymentCancelDialog(Map<String, String> params) {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => PopScope(
            canPop: false,
            child: AlertDialog(
              icon: const Icon(Icons.cancel, color: Colors.orange, size: 64),
              title: const Text('Paiement Annulé'),
              content: const Text('Vous avez annulé le paiement.'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Fermer'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _retryPayment();
                  },
                  child: const Text('Réessayer'),
                ),
              ],
            ),
          ),
    );
  }

  /// Affiche un dialog pour un paiement en attente
  void _showPaymentPendingDialog(Map<String, String> params) {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => PopScope(
            canPop: false,
            child: AlertDialog(
              icon: const Icon(
                Icons.hourglass_empty,
                color: Colors.blue,
                size: 64,
              ),
              title: const Text('Paiement en Cours'),
              content: const Text(
                'Le statut de votre paiement est en cours de vérification. '
                'Veuillez vérifier vos commandes dans quelques minutes.',
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Fermer'),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    _navigateToOrdersPage(context);
                  },
                  child: const Text('Voir mes commandes'),
                ),
              ],
            ),
          ),
    );
  }

  /// Affiche un dialog d'erreur générale
  void _showPaymentErrorDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => PopScope(
            canPop: false,
            child: AlertDialog(
              icon: const Icon(Icons.warning, color: Colors.red, size: 64),
              title: const Text('Erreur'),
              content: const Text(
                'Une erreur s\'est produite lors de la vérification du paiement. '
                'Veuillez vérifier vos commandes ou contacter le support.',
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Fermer'),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    _navigateToOrdersPage(context);
                  },
                  child: const Text('Voir mes commandes'),
                ),
              ],
            ),
          ),
    );
  }

  /// Navigue vers la page des commandes
  void _navigateToOrdersPage(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const MyOrdersScreen()));
    print('$_tag: Navigation vers la page des commandes');
  }

  /// Relance le processus de paiement
  void _retryPayment() {
    // Ici, vous devez implémenter la logique pour relancer le paiement
    // Par exemple, retourner à la page de paiement précédente
    print('$_tag: Relance du paiement');
  }

  /// Gère les changements d'état de l'application
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        _wasInBackground = true;
        print('$_tag: Application mise en arrière-plan');
        break;
      case AppLifecycleState.resumed:
        if (_wasInBackground) {
          print(
            '$_tag: Application revenue au premier plan - rafraîchissement des données',
          );
          _refreshAllAppData();
          _wasInBackground = false;
        }
        break;
      case AppLifecycleState.detached:
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }

  @override
  void dispose() {
    // Supprimer l'observer
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
