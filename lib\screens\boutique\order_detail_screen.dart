import 'package:callitris/config/api_config.dart';
import 'package:callitris/services/auth_service.dart';
import 'package:flutter/material.dart' hide DateUtils;
import 'package:flutter/services.dart';
import 'dart:ui';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../../utils/appTheme.dart';
import 'package:intl/intl.dart';
import '../../services/order_service.dart';
import '../../utils/image_utils.dart';
import '../../utils/date_utils.dart';
import '../../widgets/payment_method_selector.dart';
import '../../widgets/navigation_menu_button.dart';
import 'dart:convert';

class OrderDetailScreen extends StatefulWidget {
  final String orderId;

  const OrderDetailScreen({super.key, required this.orderId});

  @override
  State<OrderDetailScreen> createState() => _OrderDetailScreenState();
}

class _OrderDetailScreenState extends State<OrderDetailScreen>
    with WidgetsBindingObserver {
  late Map<String, dynamic> _order;
  bool _isLoading = true;
  String? _error;
  String? storageCommandeId;
  String? storageClientId;
  String? transactionCode;

  // Variables pour stocker les informations de transaction en cours
  double? _pendingMontant;
  double? _pendingMonnaie;
  String? _pendingTransactionCode;
  String? datefinCotisation = '';

  @override
  void initState() {
    super.initState();
    // Ajouter l'observateur du cycle de vie de l'application
    WidgetsBinding.instance.addObserver(this);

    // Initialiser _order avec une structure de base
    _order = {
      'versements': <dynamic>[],
      'features': <dynamic>[],
      'id': '',
      'productName': '',
      'imageUrl': 'assets/images/product_default.jpg',
      'totalPrice': 0,
      'dailyPrice': 0,
      'duration': 0,
      'versementsEffectues': 0,
      'versementsRestants': 0,
      'montantVerse': 0,
      'montantRestant': 0,
      'progress': 0.0,
      'status': 'En cours',
      'orderDate': 'N/A',
      'estimatedCompletionDate': 'N/A',
      'nextPaymentDate': 'N/A',
      'description': 'Chargement...',
      'rawData': {},
    };

    _initializeApp();
  }

  Future<void> _initPrefs() async {
    transactionCode = await OrderService.readData('payment_code');
    storageCommandeId = await OrderService.readData('commande_id');
    storageClientId = await OrderService.readData('client_id');

    try {
      if (transactionCode != null && transactionCode!.isNotEmpty) {
        await _checkPendingTransaction();
      }
    } catch (error) {
      print('Erreur lors de l\'initialisation des préférences: $error');
    }
  }

  @override
  void dispose() {
    // Supprimer l'observateur du cycle de vie de l'application
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      // L'app revient au premier plan, recharger la page
      _reloadPage();
    }
  }

  /// Initialise l'application au démarrage
  void _initializeApp() {
    _loadOrderDetails();
    _initPrefs(); // Vérifier les transactions en attente
  }

  /// Recharge la page quand l'app revient au premier plan
  void _reloadPage() {
    // Recharger les données de la page
    _initializeApp();
  }

  /// Vérifie s'il y a une transaction en attente et la traite
  Future<void> _checkPendingTransaction() async {
    try {
      // Récupérer les informations de transaction stockées
      final prefs = await SharedPreferences.getInstance();
      final pendingMontant = prefs.getDouble('pending_montant');
      final pendingMonnaie = prefs.getDouble('pending_monnaie');
      final pendingTransactionCode = prefs.getString(
        'pending_transaction_code',
      );
      transactionCode = prefs.getString('payment_code');

      print(
        'Vérification des transactions en attente: montant=$pendingMontant, monnaie=$pendingMonnaie, transactionCode=$pendingTransactionCode, payment_code=$transactionCode',
      );
      if (pendingMontant != null && pendingTransactionCode != null) {
        print(
          'Transaction en attente trouvée: $pendingTransactionCode, montant: $pendingMontant',
        );

        // Stocker les informations dans les variables de classe
        _pendingMontant = pendingMontant;
        _pendingMonnaie = pendingMonnaie ?? 0.0;
        _pendingTransactionCode = pendingTransactionCode;

        // Vérifier le statut de la transaction
        await _checkWaveTransactionStatus(transactionCode!);
      }
    } catch (e) {
      print('Erreur lors de la vérification des transactions en attente: $e');
    }
  }

  /// Vérifie le statut d'une transaction Wave spécifique
  Future<void> _checkWaveTransactionStatus(String transactionCode) async {
    try {
      final token = await AuthService.getAuthToken();

      if (token == null) {
        print('Token manquant pour vérification Wave');
        return;
      }

      final response = await http.post(
        Uri.parse("${ApiConfig.baseUrl}/paiement/wave_pay_chek.php"),
        body: {'transaction_code': transactionCode},
        headers: {'Authorization': 'Bearer $token'},
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        print('Wave transaction check: $responseData');

        if (responseData['payment_status'] == 'succeeded') {
          _showPaymentSuccess('succeeded');
        } else if (responseData['payment_status'] == 'processing') {
          _showPaymentSuccess('processing');
          print('Transaction encore en cours de traitement');
        } else {
          _showPaymentSuccess('failed');
        }
      } else {
        _showPaymentSuccess('failed');
        print('Erreur Wave check : ${response.statusCode}');
      }
    } catch (error) {
      _showPaymentSuccess('failed');
      print('Erreur lors de la vérification Wave : $error');
    }
  }

  /// Traite un paiement réussi en effectuant le versement
  Future<void> _processSuccessfulPayment() async {
    try {
      if (_pendingMontant == null) {
        print('Aucun montant en attente à traiter');
        return;
      }

      print(
        'Traitement du paiement réussi: montant=$_pendingMontant, monnaie=$_pendingMonnaie',
      );

      // Effectuer le versement via l'API
      final result = await OrderService.addVersement(
        widget.orderId,
        _pendingMontant!,
        monnaie: _pendingMonnaie ?? 0.0,
        transactionCode: transactionCode,
      );

      if (result['success']) {
        // Versement réussi
        print('Versement effectué avec succès');

        // Nettoyer les données en attente
        await _clearPendingTransaction();

        // Rafraîchir les données de la commande
        await _loadOrderDetails();

        // Afficher un message de succès
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(
                    Icons.check_circle_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Votre versement a été effectué avec succès',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
              backgroundColor: AppTheme.color.greenColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              duration: const Duration(seconds: 4),
            ),
          );
        }
      } else {
        print('Erreur lors du versement: ${result['message']}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'Erreur lors du versement'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      print('Erreur lors du traitement du paiement: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du traitement du paiement: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Stocke les informations de transaction en attente
  Future<void> _storePendingTransaction(double montant, double monnaie) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('pending_montant', montant);
      await prefs.setDouble('pending_monnaie', monnaie);

      // Stocker dans les variables de classe
      _pendingMontant = montant;
      _pendingMonnaie = monnaie;

      print(
        'Transaction en attente stockée: montant=$montant, monnaie=$monnaie',
      );
    } catch (e) {
      print('Erreur lors du stockage de la transaction en attente: $e');
    }
  }

  /// Met à jour le code de transaction en attente
  Future<void> _updatePendingTransactionCode(String transactionCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('pending_transaction_code', transactionCode);

      // Stocker dans les variables de classe
      _pendingTransactionCode = transactionCode;

      print('Code de transaction en attente mis à jour: $transactionCode');
    } catch (e) {
      print('Erreur lors de la mise à jour du code de transaction: $e');
    }
  }

  /// Nettoie les données de transaction en attente
  Future<void> _clearPendingTransaction() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('pending_montant');
      await prefs.remove('pending_monnaie');
      await prefs.remove('pending_transaction_code');

      // Nettoyer aussi les anciennes données
      await prefs.remove('payment_code');
      await prefs.remove('commande_id');
      await prefs.remove('client_id');

      // Réinitialiser les variables de classe
      _pendingMontant = null;
      _pendingMonnaie = null;
      _pendingTransactionCode = null;
      transactionCode = null;

      print('Données de transaction en attente nettoyées');
    } catch (e) {
      print('Erreur lors du nettoyage des données en attente: $e');
    }
  }

  void _showPaymentSuccess(String status) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: _buildStatusCard(status),
        );
      },
    );
  }

  Future<void> _clearPaymentCodeAndReload() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('payment_code');
    await prefs.remove('commande_id');
    await prefs.remove('client_id');
    setState(() {
      transactionCode = null;
    });
  }

  Widget _buildStatusCard(String status) {
    if (status == 'ACCEPTED' || status == 'succeeded') {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(40),
            ),
            child: Icon(Icons.check_circle, color: Colors.green, size: 50),
          ),
          const SizedBox(height: 20),
          const Text(
            'Paiement Accepté',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'Votre paiement a été vérifié avec succès!',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                // Refresh the order details to show the updated payment status
                await _processSuccessfulPayment();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: const EdgeInsets.symmetric(vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'Continuer',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
          ),
        ],
      );
    } else {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.red.shade100,
              borderRadius: BorderRadius.circular(40),
            ),
            child: Icon(Icons.error, color: Colors.red, size: 50),
          ),
          const SizedBox(height: 20),
          const Text(
            'Paiement Échoué',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'Votre paiement a été refusé. Veuillez réessayer.',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () async {
                await _clearPaymentCodeAndReload();
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                padding: const EdgeInsets.symmetric(vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'Réessayer',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
          ),
        ],
      );
    }
  }

  Future<void> _loadOrderDetails() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      print('Chargement des détails de la commande: ${widget.orderId}');

      // Récupérer les détails de la commande
      final result = await OrderService.getOrderDetails(widget.orderId);

      print('Résultat de getOrderDetails: $result');

      if (result['success']) {
        final orderData = result['data'] ?? {};

        print('Données de la commande: $orderData');

        // Récupérer l'historique des versements
        final versementsResult = await OrderService.getOrderVersements(
          widget.orderId,
        );

        print('Résultat de getOrderVersements: $versementsResult');

        // S'assurer que versements est une liste, jamais null
        final List<dynamic> versements =
            versementsResult['success']
                ? (versementsResult['versements'] ?? [])
                : [];

        // Extraire les valeurs importantes avec des valeurs par défaut sécurisées
        final int dailyPrice =
            int.tryParse(
              orderData['cout_journalier_kit']?.toString() ??
                  orderData['journalier']?.toString() ??
                  '0',
            ) ??
            0;

        final int totalDays =
            int.tryParse(orderData['jour']?.toString() ?? '0') ?? 0;
        final int paidDays =
            int.tryParse(orderData['paye']?.toString() ?? '0') ?? 0;
        final int remainingDays =
            int.tryParse(orderData['reste']?.toString() ?? '0') ?? 0;

        // Vérifier la cohérence des données
        if (paidDays + remainingDays != totalDays) {
          print(
            'Avertissement: Incohérence dans les jours - Total: $totalDays, Payés: $paidDays, Restants: $remainingDays',
          );
        }

        // Calculer les montants
        final int totalAmount = dailyPrice * totalDays;
        final int paidAmount = dailyPrice * paidDays;
        final int remainingAmount = dailyPrice * remainingDays;

        // Calculer la progression
        final double progress = totalDays > 0 ? paidDays / totalDays : 0.0;

        // Déterminer le statut
        final String status = remainingDays > 0 ? 'En cours' : 'Terminé';

        // Formater les dates
        String orderDate = 'N/A';
        String estimatedCompletionDate = 'N/A';
        String nextPaymentDate = 'N/A';

        if (orderData['date_commande'] != null &&
            orderData['date_commande'].toString().isNotEmpty) {
          try {
            // Utiliser notre classe utilitaire pour formater la date de commande
            orderDate = DateUtils.formatToFrenchDate(
              orderData['date_commande'],
            );

            // Convertir en DateTime pour les calculs
            final DateTime date = DateTime.parse(
              orderData['date_commande'].toString(),
            );

            // Date de fin estimée
            if (totalDays > 0) {
              final DateTime completionDate = date.add(
                Duration(days: totalDays),
              );
              estimatedCompletionDate = DateUtils.formatToFrenchDate(
                completionDate,
              );
            }

            // Date du prochain paiement
            if (paidDays < totalDays) {
              final DateTime nextPayment = date.add(
                Duration(days: paidDays + 1),
              );
              nextPaymentDate = DateUtils.formatToFrenchDate(nextPayment);
            } else {
              nextPaymentDate = 'Terminé';
            }
          } catch (e) {
            print('Erreur lors du formatage des dates: $e');

            // En cas d'erreur, essayer de formater directement la chaîne
            orderDate = DateUtils.formatToFrenchDate(
              orderData['date_commande'],
            );
          }
        }

        setState(() {
          datefinCotisation =
              orderData['date_fin_cotisation']?.toString() ?? 'N/A';
          _order = {
            'id': orderData['id']?.toString() ?? widget.orderId,
            'productName':
                orderData['nom_kit']?.toString() ??
                orderData['option_kit']?.toString() ??
                'Produit',
            'imageUrl':
                orderData['photo_kit'] != null
                    ? ImageUtils.formatImageUrl(
                      orderData['photo_kit'].toString(),
                    )
                    : 'assets/images/product_default.jpg',
            'totalPrice': totalAmount,
            'dailyPrice': dailyPrice,
            'duration': totalDays,
            'versementsEffectues': paidDays,
            'versementsRestants': remainingDays,
            'montantVerse': paidAmount,
            'montantRestant': remainingAmount,
            'progress': progress,
            'status': status,
            'orderDate': orderDate,
            'estimatedCompletionDate': estimatedCompletionDate,
            'nextPaymentDate': nextPaymentDate,
            'description':
                orderData['description_kit']?.toString() ??
                'Aucune description disponible',
            'versements': versements,
            // Conserver les données originales pour référence
            'rawData': orderData,
            // Ajouter des champs pour faciliter l'accès dans le reste du code
            'paye': paidDays,
            'reste': remainingDays,
            'jour': totalDays,
          };
          _isLoading = false;
        });

        print('Données de commande chargées avec succès: ${_order['id']}');
      } else {
        print('Erreur lors du chargement des détails: ${result['message']}');
        setState(() {
          _error =
              result['message'] ??
              'Erreur lors de la récupération des détails de la commande';
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Exception lors du chargement des détails: $e');
      setState(() {
        _error = 'Exception: $e';
        _isLoading = false;
      });
    }
  }

  String _formatPrice(dynamic price) {
    final formatter = NumberFormat('#,###', 'fr_FR');
    final int priceInt = price is int ? price : price.toInt();
    return formatter.format(priceInt).replaceAll(',', ' ');
  }

  // Méthode pour créer une carte de statistique simple
  Widget _buildSimpleStatCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Widget pour afficher les informations de la commande
  Widget _buildCommandeInfoCard() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade50, Colors.indigo.shade50],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.shade100),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.indigo.shade300, Colors.blue.shade400],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.shopping_bag,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _order['productName'] ?? 'Commande',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.indigo.shade800,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Code: ${_order['id'] ?? 'N/A'}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.indigo.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.shade300),
                  ),
                  child: Text(
                    'Status: ${_order['status'] ?? 'N/A'}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.green.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Widget pour l'historique des versements avec le nouveau design
  Widget _buildVersementsHistoryCard() {
    final List<dynamic> versements =
        _order.containsKey('versements')
            ? (_order['versements'] is List ? _order['versements'] : [])
            : [];

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(Icons.history, color: Colors.teal.shade600, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Historique des versements',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.indigo.shade700,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.teal.shade50,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${versements.length} versement${versements.length > 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.teal.shade700,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          versements.isEmpty
              ? const Padding(
                padding: EdgeInsets.all(40),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.receipt_long_outlined,
                        size: 48,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Aucun versement effectué',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              )
              : Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  children:
                      versements.map((versement) {
                        final index = versements.indexOf(versement);
                        return Container(
                          margin: const EdgeInsets.only(bottom: 8),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade200),
                          ),
                          child: _buildVersementListTile(versement, index),
                        );
                      }).toList(),
                ),
              ),
        ],
      ),
    );
  }

  // Widget pour créer un élément de versement dans la liste
  Widget _buildVersementListTile(dynamic versement, int index) {
    // Extraire les informations du versement avec des valeurs par défaut
    final String dateVersement =
        versement['date_vers'] ?? versement['date'] ?? 'Date inconnue';
    final String heureVersement = versement['heure_vers'] ?? '';

    // Formater la date correctement
    final String dateFormatee = DateUtils.formatToFrenchDate(dateVersement);

    // Extraire les montants
    final int montantVerse =
        int.tryParse(
          versement['montant_vers']?.toString() ??
              versement['montant_saisi']?.toString() ??
              versement['montant']?.toString() ??
              '0',
        ) ??
        0;

    final int montantPaye =
        int.tryParse(versement['montant_payer']?.toString() ?? '0') ?? 0;
    final int montantReste =
        int.tryParse(versement['montant_reste']?.toString() ?? '0') ?? 0;

    final int monnaie =
        int.tryParse(versement['monnaie']?.toString() ?? '0') ?? 0;

    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.teal.shade100,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Center(
          child: Text(
            '${index + 1}',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.teal.shade700,
            ),
          ),
        ),
      ),
      title: Text(
        '${_formatPrice(montantVerse)} FCFA',
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          Text(
            '$dateFormatee${heureVersement.isNotEmpty ? ' à $heureVersement' : ''}',
            style: TextStyle(fontSize: 13, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 4),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Journalier: ${_formatPrice(_order['dailyPrice'] ?? 0)} FCFA',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
              const SizedBox(height: 2),
              Text(
                'Monnaie utilisée: ${_formatPrice(monnaie)} FCFA',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
              const SizedBox(height: 2),
              Text(
                'Monnaie restante: ${_formatPrice(monnaie)} FCFA',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
            ],
          ),
        ],
      ),
      trailing: Icon(
        Icons.check_circle,
        color: Colors.green.shade400,
        size: 20,
      ),
    );
  }

  // Méthode pour charger la monnaie disponible
  Future<double> _loadMonnaieDisponible() async {
    try {
      // Ici vous pouvez appeler votre API pour récupérer la monnaie disponible
      // Pour l'instant, on retourne 0
      return 0.0;
    } catch (e) {
      print('Erreur lors du chargement de la monnaie: $e');
      return 0.0;
    }
  }

  void _showVersementDialog(BuildContext context, int montantJournalier) {
    _showPaymentMethodDialog(
      context,
      _order['id'].toString(),
      montantJournalier,
    );
  }

  void _showPaymentMethodDialog(
    BuildContext context,
    String orderId,
    int montantJournalier,
  ) {
    _showAmountInputDialog(context, orderId, montantJournalier);
  }

  void _showAmountInputDialog(
    BuildContext context,
    String orderId,
    int montantJournalier,
  ) {
    final TextEditingController montantController = TextEditingController();
    montantController.text = montantJournalier.toString();

    // Variables pour le calcul en temps réel
    int versementsComplets = 1; // Par défaut, 1 jour
    double monnaie = 0;
    int versementsRestants =
        int.tryParse(
          _order['reste']?.toString() ??
              _order['versementsRestants']?.toString() ??
              '0',
        ) ??
        0;
    bool excedentDetecte = false;
    PaymentMethod selectedPaymentMethod = PaymentMethod.wave;
    double monnaieDisponible = 0;

    // Fonction pour calculer les jours et la monnaie
    void calculerVersement(String value) {
      final double montantVerse = double.tryParse(value) ?? 0;
      // Calculer le nombre de jours que le montant peut couvrir
      int joursCouverts = (montantVerse / montantJournalier).floor();

      // Vérifier si ce nombre dépasse les jours restants
      excedentDetecte = joursCouverts > versementsRestants;

      if (excedentDetecte && versementsRestants > 0) {
        // Limiter les versements complets aux jours restants
        versementsComplets = versementsRestants;
        // Calculer la monnaie (tout ce qui dépasse le montant nécessaire pour les jours restants)
        monnaie = montantVerse - (versementsRestants * montantJournalier);
      } else {
        // Calcul normal
        versementsComplets = joursCouverts;
        monnaie = montantVerse - (joursCouverts * montantJournalier);
      }
    }

    // Calculer les valeurs initiales
    calculerVersement(montantController.text);

    // Charger la monnaie disponible
    _loadMonnaieDisponible().then((montant) {
      monnaieDisponible = montant;
    });

    // Utiliser un BuildContext local pour éviter les problèmes de contexte
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (BuildContext stateContext, StateSetter setState) {
            return AlertDialog(
              title: Text(
                'Effectuer un versement',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.color.textColor,
                ),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Montant journalier: ${_formatPrice(montantJournalier)} FCFA',
                      style: TextStyle(
                        color: AppTheme.color.brunGris,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Jours restants: $versementsRestants',
                      style: TextStyle(
                        color: AppTheme.color.brunGris,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Champ de saisie du montant
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Montant à verser',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.color.brunGris,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: TextField(
                            controller: montantController,
                            keyboardType: TextInputType.number,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                            decoration: InputDecoration(
                              hintText: 'Ex: 25000',
                              hintStyle: TextStyle(
                                color: const Color(0xFF8F9BB3).withOpacity(0.6),
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 18,
                              ),
                              border: InputBorder.none,
                              suffixText: 'FCFA',
                              suffixStyle: TextStyle(
                                color: AppTheme.color.primaryColor,
                                fontWeight: FontWeight.w700,
                                fontSize: 16,
                              ),
                              prefixIcon: Padding(
                                padding: const EdgeInsets.only(
                                  left: 16,
                                  right: 8,
                                ),
                                child: Icon(
                                  Icons.payments_rounded,
                                  color: AppTheme.color.primaryColor,
                                  size: 24,
                                ),
                              ),
                              prefixIconConstraints: const BoxConstraints(
                                minWidth: 0,
                                minHeight: 0,
                              ),
                            ),
                            onChanged: (value) {
                              setState(() {
                                calculerVersement(value);
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Sélecteur de méthode de paiement
                    PaymentMethodSelector(
                      selectedMethod: selectedPaymentMethod,
                      onMethodChanged: (method) {
                        setState(() {
                          selectedPaymentMethod = method;
                        });
                      },
                      montantVerse:
                          double.tryParse(montantController.text) ?? 0,
                      monnaieDisponible: monnaieDisponible,
                    ),

                    const SizedBox(height: 24),

                    // Résumé du versement
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[200]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Résumé du versement',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[800],
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Jours couverts:',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                              Text(
                                '$versementsComplets jour(s)',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Monnaie:',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                              Text(
                                '${_formatPrice(monnaie.toInt())} FCFA',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color:
                                      monnaie > 0
                                          ? AppTheme.color.greenColor
                                          : Colors.grey[800],
                                ),
                              ),
                            ],
                          ),

                          // Afficher un message d'information si un excédent est détecté
                          if (excedentDetecte && versementsRestants > 0)
                            Padding(
                              padding: const EdgeInsets.only(top: 16),
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.amber[50],
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.amber[200]!),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.info_outline,
                                      color: Colors.amber[800],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        'Le montant saisi couvre plus que les $versementsRestants jour(s) restant(s). L\'excédent de ${_formatPrice(monnaie.toInt())} FCFA sera ajouté à votre monnaie.',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.amber[900],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: Text(
                    'Annuler',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
                ElevatedButton(
                  onPressed: () async {
                    // Récupérer le montant saisi
                    final double montantVerse =
                        double.tryParse(montantController.text) ?? 0;

                    if (montantVerse <= 0) {
                      ScaffoldMessenger.of(dialogContext).showSnackBar(
                        const SnackBar(
                          content: Text('Veuillez entrer un montant valide'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }

                    // Fermer le dialogue
                    Navigator.of(dialogContext).pop();

                    // Traitement selon la méthode de paiement sélectionnée
                    if (selectedPaymentMethod.usesCinetPay) {
                      // Paiement via CinetPay
                      try {
                        final result =
                            await OrderService.addVersementWithCinetPay(
                              context: context,
                              commandeId: orderId,
                              montant: montantVerse,
                              monnaie: monnaie,
                            );

                        if (result['success']) {
                          // Rafraîchir les données de la commande
                          await _loadOrderDetails();

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                result['message'] ??
                                    'Versement effectué avec succès',
                              ),
                              backgroundColor: Colors.green,
                            ),
                          );
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                result['message'] ?? 'Erreur lors du versement',
                              ),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      } catch (e) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Erreur: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    } else if (selectedPaymentMethod == PaymentMethod.wave) {
                      // Paiement via Wave
                      try {
                        // Stocker les informations de transaction en attente
                        await _storePendingTransaction(montantVerse, monnaie);

                        final result = await OrderService.addVersementWithWave(
                          context: context,
                          commandeId: orderId,
                          montant: montantVerse,
                          monnaie: monnaie,
                        );

                        if (result['success']) {
                          if (result['paymentId'] != null) {
                            await _updatePendingTransactionCode(
                              result['paymentId'],
                            );
                          }

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                result['message'] ??
                                    'Paiement Wave initié. Vérification en cours...',
                              ),
                              backgroundColor: Colors.orange,
                            ),
                          );
                        } else {
                          // Nettoyer les données en attente en cas d'échec
                          await _clearPendingTransaction();

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                result['message'] ?? 'Erreur lors du versement',
                              ),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      } catch (e) {
                        // Nettoyer les données en attente en cas d'erreur
                        await _clearPendingTransaction();

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Erreur: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    } else {
                      // Paiement avec la monnaie (ancien système)
                      // Afficher un indicateur de chargement
                      BuildContext? loadingContext;
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (BuildContext ctx) {
                          loadingContext = ctx;
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        },
                      );

                      try {
                        // S'assurer que l'ID de commande est valide
                        final String orderId = _order['id'].toString();
                        if (orderId.isEmpty) {
                          throw Exception('ID de commande invalide');
                        }

                        print(
                          'Tentative de versement: orderId=$orderId, montant=$montantVerse, monnaie=$monnaie',
                        );

                        // Appeler l'API pour effectuer le versement
                        final result = await OrderService.addVersement(
                          orderId,
                          montantVerse,
                          monnaie: monnaie,
                        );

                        print('Résultat du versement: $result');

                        // Fermer l'indicateur de chargement en vérifiant si le contexte est toujours valide
                        if (loadingContext != null &&
                            Navigator.canPop(loadingContext!)) {
                          Navigator.pop(loadingContext!);
                        }

                        if (result['success']) {
                          // Afficher un message de confirmation
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Row(
                                children: [
                                  Icon(
                                    Icons.check_circle_rounded,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Text(
                                      excedentDetecte && versementsRestants > 0
                                          ? 'Versement de $versementsComplets jour(s) effectué avec ${_formatPrice(monnaie.toInt())} FCFA de monnaie'
                                          : (versementsComplets > 0
                                              ? 'Versement de $versementsComplets jour(s) effectué'
                                              : 'Monnaie de ${_formatPrice(monnaie.toInt())} FCFA ajoutée'),
                                      style: const TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              backgroundColor: AppTheme.color.greenColor,
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              margin: const EdgeInsets.all(16),
                              duration: const Duration(seconds: 3),
                            ),
                          );

                          // Recharger les détails de la commande après un court délai
                          // pour laisser le temps à l'API de mettre à jour les données
                          Future.delayed(const Duration(seconds: 1), () {
                            _loadOrderDetails();
                          });
                        } else {
                          // Afficher un message d'erreur
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                result['message'] ?? 'Erreur lors du versement',
                              ),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      } catch (e) {
                        print('Exception lors du versement: $e');

                        // Fermer l'indicateur de chargement en vérifiant si le contexte est toujours valide
                        if (loadingContext != null &&
                            Navigator.canPop(loadingContext!)) {
                          Navigator.pop(loadingContext!);
                        }

                        // Afficher un message d'erreur
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Erreur: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.color.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Verser',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Ajouter des logs pour déboguer
    print('Building OrderDetailScreen');
    print('_isLoading: $_isLoading');
    print('_error: $_error');
    if (!_isLoading && _error == null) {
      print('_order keys: ${_order.keys.toList()}');
      print('_order[versements] type: ${_order['versements']?.runtimeType}');
      print('_order[features] type: ${_order['features']?.runtimeType}');
    }

    if (_isLoading) {
      return _buildLoadingScreen();
    }

    if (_error != null) {
      return _buildErrorScreen();
    }

    final bool isActive = _order['status'] == 'En cours';

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 300,
              pinned: true,
              stretch: true,
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.transparent,
              elevation: 0,
              leading: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Container(
                    margin: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                ),
              ),
              actions: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                    child: Container(
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: NavigationMenuButton(
                        iconColor: const Color.fromARGB(255, 0, 0, 0),
                      ),
                    ),
                  ),
                ),
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                    child: Container(
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.share, color: Colors.white),
                        onPressed: () {
                          // Partager la commande
                        },
                      ),
                    ),
                  ),
                ),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: Stack(
                  fit: StackFit.expand,
                  children: [
                    // Image du produit avec effet parallaxe amélioré
                    Hero(
                      tag: 'product_image_${_order['id']}',
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(30),
                            bottomRight: Radius.circular(30),
                          ),
                        ),
                        clipBehavior: Clip.hardEdge,
                        child: ImageUtils.networkImageWithErrorHandling(
                          imageUrl:
                              _order['imageUrl']?.toString() ??
                              'assets/images/product_default.jpg',
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                          placeholderColor: Colors.grey[200],
                          placeholderIcon: Icons.image_not_supported_rounded,
                          placeholderIconSize: 64,
                        ),
                      ),
                    ),
                    // Dégradé amélioré pour meilleure lisibilité
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(30),
                          bottomRight: Radius.circular(30),
                        ),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withOpacity(0.3),
                            Colors.transparent,
                            Colors.black.withOpacity(0.8),
                          ],
                          stops: const [0.0, 0.4, 1.0],
                        ),
                      ),
                    ),
                    // Informations du produit en bas de l'image
                    Positioned(
                      bottom: 20,
                      left: 20,
                      right: 20,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _order['productName'],
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              letterSpacing: -0.5,
                              shadows: [
                                Shadow(
                                  blurRadius: 10,
                                  color: Colors.black54,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color:
                                      isActive
                                          ? AppTheme.color.orangeColor
                                              .withOpacity(0.9)
                                          : AppTheme.color.greenColor
                                              .withOpacity(0.9),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  _order['status'],
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w700,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                '${_formatPrice(_order['totalPrice'])} FCFA',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  shadows: [
                                    Shadow(
                                      blurRadius: 10,
                                      color: Colors.black54,
                                      offset: Offset(0, 2),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ];
        },
        body: _buildUnifiedContent(isActive),
      ),
    );
  }

  // Contenu unifié qui combine les détails et l'historique des versements
  Widget _buildUnifiedContent(bool isActive) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Carte d'informations de la commande
        _buildCommandeInfoCard(),

        // Bouton de versement si la commande est active
        if (isActive) ...[
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () async {
                _showVersementDialog(context, _order['dailyPrice']);
              },
              icon: const Icon(Icons.payment, color: Colors.white, size: 18),
              label: const Text(
                'Effectuer un versement',
                style: TextStyle(color: Colors.white, fontSize: 14),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ),
        ],
        const SizedBox(height: 16),

        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppTheme.color.redColor.withOpacity(0.1),
                AppTheme.color.redColor.withOpacity(0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: AppTheme.color.redColor.withOpacity(0.3),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: AppTheme.color.redColor.withOpacity(0.15),
                blurRadius: 12,
                offset: const Offset(0, 4),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
            child: Row(
              children: [
                // Icône d'alerte
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.color.redColor.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.schedule_outlined,
                    color: AppTheme.color.redColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // Texte principal
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Validité de la commande',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.color.redColor.withOpacity(0.8),
                          letterSpacing: 0.5,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Valable jusqu\'au $datefinCotisation',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                          color: AppTheme.color.redColor,
                          height: 1.3,
                        ),
                      ),
                    ],
                  ),
                ),

                // Icône de fin
                Icon(
                  Icons.info_outline_rounded,
                  color: AppTheme.color.redColor.withOpacity(0.7),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Statistiques de la commande
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Statistiques de la Commande',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildSimpleStatCard(
                        'Nombre de Jours',
                        _order['duration']?.toString() ?? '0',
                        Colors.blue,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildSimpleStatCard(
                        'Journalier',
                        '${_formatPrice(_order['dailyPrice'] ?? 0)} FCFA',
                        Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildSimpleStatCard(
                        'Jours Payés',
                        _order['versementsEffectues']?.toString() ?? '0',
                        Colors.teal,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildSimpleStatCard(
                        'Jours Restants',
                        _order['versementsRestants']?.toString() ?? '0',
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Historique des versements avec le nouveau design
        _buildVersementsHistoryCard(),

        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildDetailsTab(bool isActive) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Carte d'informations de la commande
        _buildCommandeInfoCard(),

        // Bouton de versement si la commande est active
        if (isActive) ...[
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () async {
                _showVersementDialog(context, _order['dailyPrice']);
              },
              icon: const Icon(Icons.payment, color: Colors.white, size: 18),
              label: const Text(
                'Effectuer un versement',
                style: TextStyle(color: Colors.white, fontSize: 14),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ),
        ],
        const SizedBox(height: 16),

        // Statistiques de la commande
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Statistiques de la Commande',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildSimpleStatCard(
                        'Nombre de Jours',
                        _order['duration']?.toString() ?? '0',
                        Colors.blue,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildSimpleStatCard(
                        'Journalier',
                        '${_formatPrice(_order['dailyPrice'] ?? 0)} FCFA',
                        Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildSimpleStatCard(
                        'Jours Payés',
                        _order['versementsEffectues']?.toString() ?? '0',
                        Colors.teal,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildSimpleStatCard(
                        'Jours Restants',
                        _order['versementsRestants']?.toString() ?? '0',
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Historique des versements avec le nouveau design
        _buildVersementsHistoryCard(),

        const SizedBox(height: 20),
      ],
    );
  }

  // Carte d'action pour le paiement (remplace le FloatingActionButton)
  Widget _buildPaymentActionCard() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () {
          _showVersementDialog(context, _order['dailyPrice']);
        },
        icon: const Icon(
          Icons.add_circle_outline,
          size: 24,
          color: Colors.white,
        ),
        label: Text(
          'Effectuer un versement'.toUpperCase(),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color.fromARGB(255, 49, 194, 100),
          foregroundColor: AppTheme.color.primaryColor,
          padding: const EdgeInsets.symmetric(vertical: 10),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
      ),
    );
  }

  Widget _buildPaymentsTab() {
    // S'assurer que versements est une liste valide
    final List<dynamic> versements =
        _order.containsKey('versements')
            ? (_order['versements'] is List ? _order['versements'] : [])
            : [];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: Colors.grey.shade200),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Historique des versements',
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.color.brunGris,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.color.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${_order['versementsEffectues']} / ${_order['duration']}',
                      style: TextStyle(
                        color: AppTheme.color.primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Liste des versements
              if (versements.isEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  child: Center(
                    child: Text(
                      'Aucun versement effectué',
                      style: TextStyle(
                        color: AppTheme.color.brunGris,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                )
              else
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  separatorBuilder: (context, index) => const Divider(),
                  itemCount: versements.length,
                  itemBuilder: (context, index) {
                    final versement = versements[index];

                    // Extraire les informations du versement avec des valeurs par défaut
                    // Utiliser les nouveaux champs de la réponse API
                    final String dateVersement =
                        versement['date_vers'] ??
                        versement['date'] ??
                        'Date inconnue';
                    final String heureVersement = versement['heure_vers'] ?? '';

                    // Formater la date correctement
                    final String dateFormatee = DateUtils.formatToFrenchDate(
                      dateVersement,
                    );

                    // Extraire les montants
                    final int montantVerse =
                        int.tryParse(
                          versement['montant_vers']?.toString() ??
                              versement['montant_saisi']?.toString() ??
                              versement['montant']?.toString() ??
                              '0',
                        ) ??
                        0;

                    final int montantPaye =
                        int.tryParse(
                          versement['montant_payer']?.toString() ?? '0',
                        ) ??
                        0;
                    final int montantReste =
                        int.tryParse(
                          versement['montant_reste']?.toString() ?? '0',
                        ) ??
                        0;

                    final int monnaie =
                        int.tryParse(versement['monnaie']?.toString() ?? '0') ??
                        0;
                    final String methode =
                        versement['methode_paiement']?.toString() ??
                        'Paiement direct';

                    return ListTile(
                      contentPadding: EdgeInsets.zero,
                      leading: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: AppTheme.color.primaryColor.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.payment,
                          color: AppTheme.color.primaryColor,
                        ),
                      ),
                      title: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Versement #${index + 1}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            dateFormatee,
                            style: TextStyle(
                              color: AppTheme.color.brunGris,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Versé: ${_formatPrice(montantVerse)} FCFA',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              ),
                              if (heureVersement.isNotEmpty)
                                Text(
                                  heureVersement,
                                  style: TextStyle(
                                    color: Colors.grey[500],
                                    fontSize: 12,
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 2),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  'Total payé: ${_formatPrice(montantPaye)} FCFA',
                                  style: TextStyle(
                                    color: AppTheme.color.greenColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  'Reste: ${_formatPrice(montantReste)} FCFA',
                                  style: TextStyle(
                                    color: AppTheme.color.orangeColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          if (monnaie > 0)
                            Padding(
                              padding: const EdgeInsets.only(top: 2),
                              child: Text(
                                'Monnaie: ${_formatPrice(monnaie)} FCFA',
                                style: TextStyle(
                                  color: AppTheme.color.greenColor,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          const SizedBox(height: 2),
                          Text(
                            'Méthode: $methode',
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),

              const SizedBox(height: 24),

              // Statistiques de paiement
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'Versé',
                      '${_formatPrice(_order['montantVerse'])} FCFA',
                      AppTheme.color.greenColor,
                      Icons.check_circle_outline,
                    ),
                  ),
                  Container(height: 40, width: 1, color: Colors.grey[200]),
                  Expanded(
                    child: _buildStatItem(
                      'Restant',
                      '${_formatPrice(_order['montantRestant'])} FCFA',
                      _order['versementsRestants'] > 0
                          ? AppTheme.color.orangeColor
                          : Colors.grey,
                      Icons.pending_outlined,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressCard(bool isActive) {
    final double progress = _order['progress'] ?? 0.0;
    final int versementsEffectues = _order['versementsEffectues'] ?? 0;
    final int versementsRestants = _order['versementsRestants'] ?? 0;
    final int totalVersements = versementsEffectues + versementsRestants;

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.color.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  Icons.trending_up,
                  color: AppTheme.color.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Progression du paiement',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.color.textColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${(progress * 100).toInt()}% complété',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.color.brunGris,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              // Badge de pourcentage
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color:
                      isActive
                          ? AppTheme.color.orangeColor.withValues(alpha: 0.1)
                          : AppTheme.color.greenColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${(progress * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color:
                        isActive
                            ? AppTheme.color.orangeColor
                            : AppTheme.color.greenColor,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Barre de progression élégante
          Container(
            height: 6,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(3),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: progress,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors:
                        isActive
                            ? [
                              AppTheme.color.primaryColor,
                              AppTheme.color.orangeColor,
                            ]
                            : [
                              AppTheme.color.greenColor.withValues(alpha: 0.7),
                              AppTheme.color.greenColor,
                            ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  '$versementsEffectues versements effectués',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.color.brunGris,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  '$versementsRestants restants',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.color.brunGris,
                  ),
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Statistiques avec design épuré
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppTheme.color.greenColor.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppTheme.color.greenColor.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.check_circle_outline,
                            color: AppTheme.color.greenColor,
                            size: 16,
                          ),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              'Montant versé',
                              style: TextStyle(
                                fontSize: 12,
                                color: AppTheme.color.brunGris,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${_formatPrice(_order['montantVerse'])} FCFA',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.color.greenColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: (isActive ? AppTheme.color.orangeColor : Colors.grey)
                        .withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: (isActive
                              ? AppTheme.color.orangeColor
                              : Colors.grey)
                          .withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.pending_outlined,
                            color:
                                isActive
                                    ? AppTheme.color.orangeColor
                                    : Colors.grey,
                            size: 16,
                          ),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              'Montant restant',
                              style: TextStyle(
                                fontSize: 12,
                                color: AppTheme.color.brunGris,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${_formatPrice(_order['montantRestant'])} FCFA',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color:
                              isActive
                                  ? AppTheme.color.orangeColor
                                  : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(color: AppTheme.color.brunGris, fontSize: 14),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentInfoCard(bool isActive) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations de paiement',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.brunGris,
              ),
            ),
            const SizedBox(height: 16),

            _buildInfoRow(
              'Prix journalier',
              '${_formatPrice(_order['dailyPrice'])} FCFA',
            ),
            _buildInfoRow('Durée totale', '${_order['duration']} jours'),
            _buildInfoRow(
              'Montant total',
              '${_formatPrice(_order['totalPrice'])} FCFA',
            ),
            const Divider(height: 32),
            _buildInfoRow(
              'Jours payés',
              '${_order['versementsEffectues']} jours',
            ),
            _buildInfoRow(
              'Jours restants',
              '${_order['versementsRestants']} jours',
            ),

            if (!isActive) ...[
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.color.greenColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: AppTheme.color.greenColor),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Commande terminée',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: AppTheme.color.greenColor,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Tous les versements ont été effectués',
                            style: TextStyle(
                              color: AppTheme.color.brunGris,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(color: AppTheme.color.brunGris, fontSize: 15),
          ),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: const Color(0xFFF9FAFC),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Détails de la commande',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: Color(0xFF0A0A0A),
            letterSpacing: -0.3,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF0A0A0A)),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [NavigationMenuButton()],
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 40,
              height: 40,
              child: CircularProgressIndicator(
                color: AppTheme.color.primaryColor,
                strokeWidth: 3,
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Chargement des détails...',
              style: TextStyle(
                color: Color(0xFF8F9BB3),
                fontSize: 15,
                letterSpacing: -0.2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen() {
    return Scaffold(
      backgroundColor: const Color(0xFFF9FAFC),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Détails de la commande',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: Color(0xFF0A0A0A),
            letterSpacing: -0.3,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF0A0A0A)),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [NavigationMenuButton()],
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 96,
                height: 96,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.error_outline,
                  size: 48,
                  color: Colors.grey[500],
                ),
              ),
              const SizedBox(height: 28),
              const Text(
                'Commande introuvable',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF0A0A0A),
                  letterSpacing: -0.3,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                _error!,
                style: TextStyle(
                  color: AppTheme.color.brunGris,
                  fontSize: 16,
                  height: 1.4,
                  letterSpacing: -0.2,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: 220,
                height: 52,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.color.primaryColor,
                    foregroundColor: Colors.white,
                    elevation: 0,
                    shadowColor: AppTheme.color.primaryColor.withOpacity(0.3),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: const Text(
                    'Retour',
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                      fontSize: 16,
                      letterSpacing: -0.2,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
